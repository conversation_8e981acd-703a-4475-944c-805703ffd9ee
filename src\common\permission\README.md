# 公共权限管理系统

## 概述

这是一个统一的权限管理系统，采用分层模块化架构设计，实现了PC端和移动端的统一权限管理。系统支持多种权限类型、灵活的权限检查策略，并与Vue深度集成。

## 文件结构

```
src/common/permission/
├── constant.js                    # 权限相关常量定义
├── BasePermissionManager.js       # 权限管理器基类
├── PermissionManager.js          # 主权限管理器（单例模式）
├── RoutePermissionManager.js     # 路由权限管理器
├── ComponentPermissionManager.js # 组件权限管理器
├── FeaturePermissionManager.js   # 功能权限管理器
├── RegionPermissionManager.js    # 区域权限管理器
├── ConversationPermissionManager.js # 会话权限管理器
├── index.js                      # 入口文件，包含Vue插件
└── README.md                     # 说明文档
```

## 架构设计

### 🏗️ 分层架构
- **基础层**: BasePermissionManager 提供通用接口和缓存机制
- **管理层**: PermissionManager 统一协调所有子权限管理器
- **专业层**: 各种专业权限管理器处理特定类型的权限
- **集成层**: Vue插件、指令、混入等前端集成

### 🔐 权限类型体系

#### 用户角色常量 (constant.js)
```javascript
export const USER_ROLE = Object.freeze({
    TEMP_USER: 0,        // 临时用户
    NORMAL_USER: 1,      // 普通用户
    ADMIN: 2,            // 管理员
    SUPER_ADMIN: 3,      // 超级管理员
    DIRECTOR: 4,         // 主任
});

export const GROUP_ROLE = Object.freeze({
    NORMAL: 0,           // 普通成员
    MANAGER: 1,          // 管理员
    CREATOR: 2,          // 创建者
});
```

### 🎯 权限管理器详解

#### BasePermissionManager
- 所有权限管理器的抽象基类
- 提供通用的权限检查接口和缓存机制
- 定义标准的初始化和权限检查流程

#### PermissionManager
- **单例模式**的主权限管理器
- 统一协调所有子权限管理器
- 提供统一的权限检查入口点
- 支持两阶段初始化：区域权限初始化 + 用户权限初始化

#### RoutePermissionManager
- 路由级别的权限控制
- 支持白名单路由机制
- 支持动态路由权限配置
- 自动从路由配置中提取权限信息
- 支持OR/AND权限检查策略
- 已集成到PC端路由守卫中
- 提供全局单例模式

#### ComponentPermissionManager
- 组件级别的权限控制
- 支持组件显示/隐藏/禁用控制
- 支持元素级权限控制
- 批量权限检查功能

#### FeaturePermissionManager
- 功能级别的权限控制
- 支持 feature.action 格式的权限标识
- API权限控制
- 数据权限控制

#### RegionPermissionManager
- 基于区域配置的功能权限
- 支持区域功能开关
- 权限映射机制
- 独立于用户登录状态

#### ConversationPermissionManager
- 会话内的角色权限控制
- 支持群主、管理员、普通成员等角色
- 消息、成员管理、资源共享等权限控制
- 支持动态角色变更

## 使用方法

### 🚀 1. 在Vue项目中安装插件

```javascript
import Vue from 'vue';
import { install as PermissionPlugin } from '@/common/permission';
import permissionManager from '@/common/permission';

// 安装权限插件
Vue.use(PermissionPlugin, {
    autoInit: false, // 是否自动初始化
    components: [] // 需要权限混入的组件名称列表
});
```

### ⚙️ 2. 初始化权限管理器

#### 应用启动时初始化区域权限
```javascript
// 在 main.js 或应用入口文件中
import permissionManager from '@/common/permission';

// 初始化区域权限（应用启动时，不依赖用户登录）
permissionManager.initializeRegionPermissions().then(() => {
    console.log('区域权限管理器初始化完成');
}).catch(error => {
    console.error('区域权限管理器初始化失败:', error);
});
```

#### 用户登录后初始化用户权限
```javascript
// 在用户登录成功后调用
const userInfo = {
    uid: 123,
    role: 2, // USER_ROLE.ADMIN
    username: 'admin',
    // ... 其他用户信息
};

await permissionManager.initialize(userInfo, config);
```

### 🔍 3. 权限检查方法

#### 基础权限检查
```javascript
// 检查功能权限
const hasPermission = permissionManager.checkFeaturePermission('backgroundManage');

// 检查路由权限
const canAccess = permissionManager.checkRoutePermission('/admin');

// 检查组件权限
const isVisible = permissionManager.checkComponentPermission('button', 'delete');

// 检查区域权限
const isEnabled = permissionManager.checkRegionPermission('tvwall');
```

#### 通用权限检查（推荐）
```javascript
// 字符串模式 - 检查功能权限
const hasAccess = permissionManager.checkPermission('backgroundManage');

// 数组模式 - 多权限检查（AND策略）
const hasAllPermissions = permissionManager.checkPermission(['user_manage', 'group_manage']);

// 对象模式 - 复合权限检查
const hasComplexPermission = permissionManager.checkPermission({
    regionPermissionKey: 'live',
    featurePermissionKey: 'liveStart',
    strategy: 'OR' // 或 'AND'
});

// 对象模式 - 权限数组检查
const hasAnyPermission = permissionManager.checkPermission({
    permissions: ['admin', 'super_admin'],
    strategy: 'OR'
});
```

#### 用户角色检查
```javascript
// 检查用户角色
const isAdmin = permissionManager.isAdmin();
const isSuperAdmin = permissionManager.isSuperAdmin();
const userRole = permissionManager.getUserRole();
const userId = permissionManager.getUserId();
```

### 🎯 4. 在Vue组件中使用

#### 基础使用
```javascript
export default {
    computed: {
        canDelete() {
            return this.$checkComponent('button', 'delete');
        },
        isAdmin() {
            return this.$isAdmin();
        },
        canAccessAdmin() {
            return this.$checkRoute('/admin');
        }
    }
}
```

#### 响应式权限组件
```javascript
export default {
    // 启用权限响应式混入
    permission: true, // 或 { reactive: true }

    computed: {
        // 权限变化时会自动重新计算
        canManageUsers() {
            return this.$checkPermission('user_manage');
        }
    },

    methods: {
        // 权限变化时的回调
        onPermissionChanged(changeInfo) {
            console.log('权限发生变化:', changeInfo);
        }
    }
}
```

### 🎨 5. 使用v-permission指令

#### 基础指令用法
```html
<!-- 功能权限 -->
<button v-permission="'backgroundManage'">后台管理</button>

<!-- 组件权限 -->
<button v-permission.component="'delete'">删除</button>

<!-- 路由权限 -->
<router-link v-permission.route="'/admin'">管理页面</router-link>

<!-- 区域权限 -->
<div v-permission.region="'tvwall'">电视墙功能</div>

<!-- 功能权限（明确指定） -->
<button v-permission.feature="'liveStart'">开始直播</button>
```

#### 高级指令用法
```html
<!-- 复合权限对象 -->
<button v-permission="{
    regionPermissionKey: 'live',
    featurePermissionKey: 'liveStart'
}">开始直播</button>

<!-- 权限检查配置 -->
<div v-permission="{
    type: 'feature',
    permission: 'admin_access',
    context: { departmentId: 123 }
}">部门管理</div>

<!-- 会话权限 -->
<button v-permission.conversation="'message.delete'">删除消息</button>
```

#### 指令修饰符
```html
<!-- 隐藏模式（默认是移除DOM） -->
<div v-permission.hide="'admin'">管理员内容</div>

<!-- 禁用模式 -->
<button v-permission.disable="'delete'">删除按钮</button>

<!-- 组合使用 -->
<button v-permission.component.disable="'edit'">编辑按钮</button>
```

## 🔄 路由权限集成

### PC端路由守卫集成
PC端已完全集成路由权限检查：

```javascript
// 在 src/module/ultrasync_pc/router/index.js 中
import { RoutePermissionManager } from '@/common/permission';
import permissionManager from '@/common/permission';

// 路由守卫中的权限检查
router.beforeEach(async (to, from, next) => {
    // 检查是否是需要权限控制的路由
    if (RoutePermissionManager.isProtectedRoute(to.path)) {
        // 确保权限管理器已初始化
        if (!permissionManager.isInitialized()) {
            const user = window.vm && window.vm.$store && window.vm.$store.state.user;
            if (user && user.uid) {
                await permissionManager.initialize(user);
            } else {
                next('/login');
                return;
            }
        }

        // 进行权限检查
        if (!RoutePermissionManager.checkRouteAccess(to.path)) {
            next('/no-permission');
            return;
        }
    }
    next();
});

// 初始化路由权限管理器
const routePermissionManager = RoutePermissionManager.getGlobalInstance();
routePermissionManager.setRouterConfig(router, whiteList);
```

### 移动端路由权限支持
移动端已初始化路由权限管理器，支持权限配置：

```javascript
// 在 src/module/ultrasync/router/index.js 中
import { RoutePermissionManager } from '@/common/permission';

const whiteList = ['/login', '/register', '/forgetPassword'];

// 初始化路由权限管理器
const routePermissionManager = RoutePermissionManager.getGlobalInstance();
routePermissionManager.setRouterConfig(router, whiteList);
```

### 路由权限配置
在路由配置中添加权限信息：

```javascript
{
    path: 'background_manage',
    name: 'background_manage',
    component: BackgroundManage,
    meta: {
        roles: [USER_ROLE.ADMIN, USER_ROLE.SUPER_ADMIN],
        permissions: ['admin'],
        strategy: 'OR' // 或 'AND'
    }
}
```

## 🔧 项目集成状态

### PC端项目 (ultrasync_pc)
- ✅ **权限插件**: 已安装并配置
- ✅ **区域权限**: 应用启动时自动初始化
- ✅ **用户权限**: 登录后自动初始化
- ✅ **路由守卫**: 完全集成，自动权限检查
- ✅ **Vue集成**: 支持指令、混入、原型方法

### 移动端项目 (ultrasync)
- ✅ **权限插件**: 已安装并配置
- ✅ **区域权限**: 应用启动时自动初始化
- ✅ **路由权限**: 管理器已初始化
- ⚠️ **路由守卫**: 未添加，需要手动集成
- ✅ **Vue集成**: 支持指令、混入、原型方法

## 💡 最佳实践

### 1. 权限检查优先级
1. 使用通用的 `checkPermission()` 方法
2. 优先使用对象模式进行复合权限检查
3. 在组件中使用响应式权限混入
4. 使用 v-permission 指令进行UI控制

### 2. 性能优化
- 权限检查结果会自动缓存
- 避免在循环中进行权限检查
- 使用批量权限检查方法
- 合理设置缓存TTL

### 3. 错误处理
- 权限管理器未初始化时会返回 false
- 监听权限变化事件进行UI更新
- 在路由守卫中处理权限不足的情况

## 🚀 扩展开发

### 添加新的权限管理器
1. 继承 `BasePermissionManager` 创建新的权限管理器
2. 在 `PermissionManager` 中集成新的管理器
3. 在 `index.js` 中导出新的接口

```javascript
// 示例：创建新的权限管理器
class CustomPermissionManager extends BasePermissionManager {
    async loadPermissions() {
        // 实现权限加载逻辑
    }

    hasPermission(permission, context = {}) {
        // 实现权限检查逻辑
    }
}
```

### 自定义权限检查逻辑
```javascript
// 扩展权限检查方法
permissionManager.addCustomChecker('department', (permission, context) => {
    // 自定义部门权限检查逻辑
    return checkDepartmentPermission(permission, context);
});
```

## 📚 API参考

### PermissionManager 主要方法

```javascript
// 初始化方法
await permissionManager.initializeRegionPermissions(config)
await permissionManager.initialize(userInfo, config)

// 权限检查方法
permissionManager.checkPermission(permission, context)
permissionManager.checkRoutePermission(routePath, context)
permissionManager.checkComponentPermission(component, action, context)
permissionManager.checkFeaturePermission(feature, action, context)
permissionManager.checkRegionPermission(functionName, context)
permissionManager.checkConversationPermission(permission, context)

// 用户信息方法
permissionManager.getUserInfo()
permissionManager.getUserRole()
permissionManager.getUserId()
permissionManager.isAdmin()
permissionManager.isSuperAdmin()

// 状态方法
permissionManager.isInitialized()
permissionManager.clearCache()
permissionManager.destroy()
permissionManager.logoutCleanup() // 退出登录清理（保留区域权限）
```

### Vue原型方法

```javascript
// 在Vue组件中可直接使用
this.$checkPermission(permission, context)
this.$checkRoute(routePath, context)
this.$checkComponent(component, action, context)
this.$checkFeature(feature, action, context)
this.$checkRegionFunction(functionName, context)
this.$isAdmin()
this.$isSuperAdmin()
this.$getUserRole()
```

### 事件系统

```javascript
// 监听权限变化事件
window.addEventListener('permission:initialized', (event) => {
    console.log('权限管理器初始化完成', event.detail);
});

window.addEventListener('permission:changed', (event) => {
    console.log('权限发生变化', event.detail);
});

window.addEventListener('permission:regionChanged', (event) => {
    console.log('区域权限发生变化', event.detail);
});

window.addEventListener('permission:conversationChanged', (event) => {
    console.log('会话权限发生变化', event.detail);
});
```

## ⚠️ 注意事项

### 1. 初始化顺序
- 应用启动时先初始化区域权限
- 用户登录后再初始化用户权限
- 确保在使用权限检查前完成初始化

### 2. 缓存机制
- 权限检查结果会被缓存5分钟
- 用户信息变化时会自动清除缓存
- 可手动调用 `clearCache()` 清除缓存

### 3. 错误处理
- 未初始化时权限检查返回 `false`
- 权限检查异常时会输出警告日志
- 建议在关键权限检查处添加错误处理

### 4. 性能考虑
- 避免在渲染循环中进行权限检查
- 使用计算属性缓存权限检查结果
- 合理使用批量权限检查方法

### 5. 向后兼容
- PC端项目的现有代码无需修改
- 所有API保持向后兼容
- 新功能通过可选参数扩展

## 🔍 故障排除

### 常见问题

1. **权限检查总是返回false**
   - 检查权限管理器是否已初始化
   - 确认用户信息是否正确传入
   - 验证权限配置是否正确

2. **路由权限不生效**
   - 确认路由权限管理器已正确初始化
   - 检查路由配置中的权限信息
   - 验证白名单配置

3. **权限变化不响应**
   - 确认组件已启用权限混入
   - 检查事件监听器是否正确设置
   - 验证权限版本号是否更新

### 调试方法

```javascript
// 开启调试模式
permissionManager.setDebugMode(true);

// 查看当前权限状态
console.log('权限管理器状态:', {
    initialized: permissionManager.isInitialized(),
    userInfo: permissionManager.getUserInfo(),
    userRole: permissionManager.getUserRole()
});

// 查看缓存状态
console.log('权限缓存:', permissionManager.getCacheInfo());
```

## 🚪 退出登录处理

### logoutCleanup() 方法

权限系统提供了专门的退出登录清理方法，用于在用户退出登录时清理用户相关的权限数据，同时保留区域权限配置：

```javascript
// 退出登录时调用
permissionManager.logoutCleanup();
```

### 与 destroy() 的区别

- **logoutCleanup()**: 只清理用户相关的权限管理器，保留区域权限不变
- **destroy()**: 完全销毁所有权限管理器，包括区域权限

### 清理内容

logoutCleanup() 会清理：
- 路由权限管理器的用户数据（保留路由配置和全局实例）
- 组件权限管理器（完全销毁）
- 功能权限管理器（完全销毁）
- 会话权限管理器（完全销毁）
- 用户相关的权限缓存

保留内容：
- 区域权限管理器及其配置
- 区域权限的初始化状态
- 路由权限管理器的全局实例和路由配置
- 权限管理器单例实例

### 使用场景

在以下场景中应该使用 `logoutCleanup()` 而不是 `destroy()`:
- 用户主动退出登录
- 切换用户账户
- Token过期自动退出
- 其他需要清理用户权限但保留系统配置的场景
