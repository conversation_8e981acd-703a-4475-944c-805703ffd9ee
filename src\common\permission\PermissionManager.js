import { USER_ROLE } from './constant.js';
import BasePermissionManager from './BasePermissionManager.js';
import RoutePermissionManager from './RoutePermissionManager.js';
import ComponentPermissionManager from './ComponentPermissionManager.js';
import FeaturePermissionManager from './FeaturePermissionManager.js';
import RegionPermissionManager from './RegionPermissionManager.js';
import ConversationPermissionManager from './ConversationPermissionManager.js';

/**
 * 主权限管理器单例
 * 统一管理所有子权限管理器，提供统一的权限检查接口
 */
class PermissionManager extends BasePermissionManager {
    constructor() {
        if (PermissionManager.instance) {
            return PermissionManager.instance;
        }

        // 调用父类构造函数
        super();

        // 延迟初始化其他权限管理器，只在构造函数中初始化区域权限管理器
        this.routeManager = null;
        this.componentManager = null;
        this.featureManager = null;
        this.regionManager = new RegionPermissionManager();
        this.conversationManager = null;

        // 区域权限初始化状态（应用初始化时的权限）
        this.regionInitialized = false;

        // 缓存清理相关
        this.cacheCleanupInterval = null; // 缓存清理定时器
        this.cacheCleanupIntervalMs = 10 * 60 * 1000; // 10分钟清理一次过期缓存

        PermissionManager.instance = this;
    }

    /**
     * 获取单例实例
     * @returns {PermissionManager} 权限管理器实例
     */
    static getInstance() {
        if (!PermissionManager.instance) {
            PermissionManager.instance = new PermissionManager();
        }
        return PermissionManager.instance;
    }

    /**
     * 初始化区域权限管理器（在应用初始化时调用）
     * @param {Object} config - 配置信息
     */
    async initializeRegionPermissions(config = {}) {
        try {
            if (this.regionInitialized) {
                console.log('RegionPermissionManager already initialized');
                return;
            }

            this.config = { ...this.config, ...config };

            // 只初始化区域权限管理器
            await this.regionManager.initialize(null, this.config);
            this.regionInitialized = true;

            // 触发区域权限初始化完成事件
            window.vm.$root.eventBus.$emit('permission:regionInitialized', { config: this.config });

            console.log('RegionPermissionManager initialized successfully');
        } catch (error) {
            console.error('Failed to initialize RegionPermissionManager:', error);
            throw error;
        }
    }

    /**
     * 初始化用户权限管理器（在用户登录后调用）
     * @param {Object} userInfo - 用户信息
     * @param {Object} config - 配置信息
     */
    async initialize(userInfo, config = {}) {
        try {
            // 调用父类的初始化方法
            await super.initialize(userInfo, config);

            // 延迟创建其他权限管理器
            if (!this.routeManager) {
                // 使用已存在的全局实例，如果没有则创建新的
                this.routeManager = RoutePermissionManager.getGlobalInstance();
                this.componentManager = new ComponentPermissionManager();
                this.featureManager = new FeaturePermissionManager();
                this.conversationManager = new ConversationPermissionManager();

                // 确保全局实例指向当前的routeManager
                RoutePermissionManager.setGlobalInstance(this.routeManager);
            }

            // 初始化用户相关的权限管理器
            await Promise.all([
                this.routeManager.initialize(userInfo, this.config),
                this.componentManager.initialize(userInfo, this.config),
                this.featureManager.initialize(userInfo, this.config),
                this.conversationManager.initialize(userInfo, this.config)
            ]);

            // 触发初始化完成事件
            this.emitEvent('initialized', { userInfo, config: this.config });

            // 启动缓存清理定时器
            this.startCacheCleanup();

            console.log('PermissionManager initialized successfully', userInfo);
        } catch (error) {
            console.error('Failed to initialize PermissionManager:', error);
            throw error;
        }
    }

    /**
     * 加载权限数据（实现基类的抽象方法）
     * PermissionManager 本身不直接加载权限，而是通过子管理器加载
     */
    async loadPermissions() {
        // PermissionManager 本身不需要加载权限数据
        // 权限数据由各个子管理器负责加载
        console.log('PermissionManager loadPermissions called');
    }

    /**
     * 检查路由权限
     * @param {string} routePath - 路由路径
     * @param {Object} context - 上下文信息
     * @returns {boolean} 是否有权限
     */
    checkRoutePermission(routePath, context = {}) {
        if (!this.initialized || !this.routeManager) {
            console.warn('RoutePermissionManager not initialized, returning false for route:', routePath);
            return false;
        }
        return this.routeManager.hasPermission(routePath, context);
    }

    /**
     * 检查组件权限
     * @param {string} component - 组件名称
     * @param {string} action - 操作名称
     * @param {Object} context - 上下文信息
     * @returns {boolean} 是否有权限
     */
    checkComponentPermission(component, action = null, context = {}) {
        if (!this.initialized || !this.componentManager) {
            console.warn('ComponentPermissionManager not initialized, returning false for component:', component);
            return false;
        }
        return this.componentManager.hasPermission(component, action, context);
    }

    /**
     * 检查功能权限
     * @param {string} feature - 功能名称
     * @param {string} action - 操作名称
     * @param {Object} context - 上下文信息
     * @returns {boolean} 是否有权限
     */
    checkFeaturePermission(feature, action = null, context = {}) {
        if (!this.initialized || !this.featureManager) {
            console.warn('FeaturePermissionManager not initialized, returning false for feature:', feature);
            return false;
        }
        return this.featureManager.hasPermission(feature, action, context);
    }

    /**
     * 检查区域功能权限
     * @param {string} functionName - 功能名称
     * @param {Object} context - 上下文信息
     * @returns {boolean} 是否有权限
     */
    checkRegionPermission(functionName, context = {}) {
        if (!this.regionInitialized) {
            console.warn('RegionPermissionManager not initialized, returning false for region function:', functionName);
            return false;
        }
        return this.regionManager.hasPermission(functionName, context);
    }

    /**
     * 检查会话权限
     * @param {string} permission - 权限标识 (格式: feature.action)
     * @param {Object} context - 上下文信息
     * @param {string} context.conversationId - 会话ID
     * @param {string} context.userId - 用户ID (可选，默认使用当前用户)
     * @returns {boolean} 是否有权限
     */
    checkConversationPermission(permission, context = {}) {
        if (!this.initialized || !this.conversationManager) {
            console.warn('ConversationPermissionManager not initialized, returning false for permission:', permission);
            return false;
        }
        return this.conversationManager.hasPermission(permission, context);
    }

    /**
     * 通用权限检查方法
     * 支持多种参数格式：
     * 1. 字符串模式：直接传给checkFeaturePermission，不考虑区域配置
     * 2. 数组模式：支持多个权限检查，默认策略为AND（所有权限都必须通过）
     * 3. 对象模式：支持以下情况
     *    - 只传 regionPermissionKey：只校验区域配置
     *    - 只传 featurePermissionKey：只校验功能权限
     *    - 只传 conversationPermissionKey：只校验会话权限
     *    - 传入多个权限类型：按照指定策略检查
     *    - 传入 permissions 数组：支持批量权限检查
     *
     * @param {string|Array|Object} permission - 权限标识
     *   - 字符串：直接作为功能权限检查
     *   - 数组：多个权限标识，默认AND策略
     *   - 对象：{
     *       regionPermissionKey?: string,
     *       featurePermissionKey?: string,
     *       conversationPermissionKey?: string,
     *       permissions?: Array<string|Object>,
     *       strategy?: 'AND'|'OR'
     *     }
     * @param {Object} context - 上下文信息
     * @returns {boolean} 是否有权限
     *
     * @example
     * // 字符串模式 - 检查单个功能权限
     * checkPermission('user_manage')
     *
     * // 数组模式 - 检查多个权限，默认AND策略（都必须有权限）
     * checkPermission(['user_manage', 'group_manage'])
     *
     * // 对象模式 - 只检查区域权限
     * checkPermission({ regionPermissionKey: 'chat_function' })
     *
     * // 对象模式 - 只检查功能权限
     * checkPermission({ featurePermissionKey: 'user_manage' })
     *
     * // 对象模式 - 只检查会话权限
     * checkPermission({ conversationPermissionKey: 'message.send' }, { conversationId: 'conv123' })
     *
     * // 对象模式 - 检查多个权限类型，默认AND策略
     * checkPermission({
     *   regionPermissionKey: 'chat_function',
     *   featurePermissionKey: 'user_manage'
     * })
     *
     * // 对象模式 - 检查多个权限类型，OR策略（有一个权限即可）
     * checkPermission({
     *   regionPermissionKey: 'chat_function',
     *   featurePermissionKey: 'user_manage',
     *   strategy: 'OR'
     * })
     *
     * // 对象模式 - 批量检查权限数组，AND策略
     * checkPermission({
     *   permissions: ['user_manage', 'group_manage'],
     *   strategy: 'AND'
     * })
     *
     * // 对象模式 - 批量检查权限数组，OR策略
     * checkPermission({
     *   permissions: ['user_manage', 'group_manage'],
     *   strategy: 'OR'
     * })
     *
     * // 对象模式 - 混合权限检查，OR策略
     * checkPermission({
     *   featurePermissionKey: 'admin_access',
     *   conversationPermissionKey: 'message.manage',
     *   strategy: 'OR'
     * }, { conversationId: 'conv123' })
     */
    checkPermission(permission, context = {}) {
        // 如果未初始化，返回false而不是抛出错误
        let hasPermission = false;
        if (!this.initialized) {
            console.warn('PermissionManager not initialized, returning false for permission:', permission);
            return false;
        }

        // 字符串模式：直接检查功能权限，不考虑区域配置
        if (typeof permission === 'string') {
            hasPermission = this.checkSinglePermission(permission, context);
        }

        // 数组模式：多个权限检查，默认AND策略
        if (Array.isArray(permission)) {
            hasPermission = this.checkMultiplePermissions(permission, 'AND', context);
        }

        // 对象模式：需要验证参数格式
        if (typeof permission === 'object' && permission !== null) {
            hasPermission = this.checkObjectPermission(permission, context);
        }
        return hasPermission;
    }

    /**
     * 检查单个权限（字符串模式）
     * @param {string} permission - 权限标识
     * @param {Object} context - 上下文信息
     * @returns {boolean} 是否有权限
     */
    checkSinglePermission(permission, context = {}) {
        // 使用缓存机制包装
        return this.checkWithCache('general', permission, context, () => {
            return this.checkFeaturePermission(permission, null, context);
        });
    }

    /**
     * 检查多个权限（数组模式）
     * @param {Array} permissions - 权限标识数组
     * @param {string} strategy - 检查策略 'AND' | 'OR'
     * @param {Object} context - 上下文信息
     * @returns {boolean} 是否有权限
     */
    checkMultiplePermissions(permissions, strategy = 'AND', context = {}) {
        if (!Array.isArray(permissions) || permissions.length === 0) {
            console.warn('checkMultiplePermissions: permissions must be a non-empty array');
            return false;
        }

        // 生成更安全的缓存键
        const cacheKey = `multi_${strategy}_${permissions.map(perm =>
            typeof perm === 'string' ? perm : JSON.stringify(perm)
        ).join('_')}`;

        return this.checkWithCache('multiple', cacheKey, context, () => {
            if (strategy === 'OR') {
                // OR策略：有一个权限通过就返回true
                return permissions.some(perm => this.checkSinglePermissionInternal(perm, context));
            } else {
                // AND策略：所有权限都必须通过
                return permissions.every(perm => this.checkSinglePermissionInternal(perm, context));
            }
        });
    }

    /**
     * 检查对象权限（对象模式）
     * @param {Object} permission - 权限对象
     * @param {Object} context - 上下文信息
     * @returns {boolean} 是否有权限
     */
    checkObjectPermission(permission, context = {}) {
        const {
            regionPermissionKey,
            featurePermissionKey,
            conversationPermissionKey,
            permissions,
            strategy = 'AND'
        } = permission;

        // 如果传入了 permissions 数组，使用批量检查
        if (permissions && Array.isArray(permissions)) {
            return this.checkMultiplePermissions(permissions, strategy, context);
        }

        // 检查是否至少传入了一个权限类型
        if (!regionPermissionKey && !featurePermissionKey && !conversationPermissionKey) {
            console.error('checkPermission: at least one permission key is required when permission is an object');
            return false;
        }

        // 收集需要检查的权限
        const permissionsToCheck = [];

        if (regionPermissionKey) {
            permissionsToCheck.push({
                type: 'region',
                key: regionPermissionKey
            });
        }

        if (featurePermissionKey) {
            permissionsToCheck.push({
                type: 'feature',
                key: featurePermissionKey
            });
        }

        if (conversationPermissionKey) {
            permissionsToCheck.push({
                type: 'conversation',
                key: conversationPermissionKey
            });
        }

        // 根据策略检查权限
        const cacheKey = `obj_${strategy}_${permissionsToCheck.map(p => `${p.type}:${p.key}`).join('_')}`;
        return this.checkWithCache('object', cacheKey, context, () => {
            if (strategy === 'OR') {
                // OR策略：有一个权限通过就返回true
                return permissionsToCheck.some(perm => this.checkPermissionByType(perm.type, perm.key, context));
            } else {
                // AND策略：所有权限都必须通过
                return permissionsToCheck.every(perm => this.checkPermissionByType(perm.type, perm.key, context));
            }
        });
    }

    /**
     * 根据权限类型检查权限
     * @param {string} type - 权限类型 'region' | 'feature' | 'conversation'
     * @param {string} key - 权限标识
     * @param {Object} context - 上下文信息
     * @returns {boolean} 是否有权限
     */
    checkPermissionByType(type, key, context = {}) {
        switch (type) {
        case 'region':
            if (!this.regionInitialized) {
                console.warn('RegionPermissionManager not initialized, returning false for region permission:', key);
                return false;
            }
            return this.checkRegionPermission(key, context);

        case 'feature':
            return this.checkFeaturePermission(key, null, context);

        case 'conversation':
            if (!this.conversationManager) {
                console.warn('ConversationPermissionManager not initialized, returning false for conversation permission:', key);
                return false;
            }
            return this.checkConversationPermission(key, context);

        default:
            console.error('checkPermissionByType: unknown permission type:', type);
            return false;
        }
    }

    /**
     * 内部单个权限检查方法（不使用缓存）
     * @param {string|Object} permission - 权限标识
     * @param {Object} context - 上下文信息
     * @returns {boolean} 是否有权限
     */
    checkSinglePermissionInternal(permission, context = {}) {
        if (typeof permission === 'string') {
            return this.checkFeaturePermission(permission, null, context);
        }

        if (typeof permission === 'object' && permission !== null) {
            return this.checkObjectPermission(permission, context);
        }

        return false;
    }

    /**
     * 更新用户信息（重写基类方法，添加子管理器更新逻辑）
     * @param {Object} userInfo - 新的用户信息
     */
    updateUserInfo(userInfo) {
        const oldRole = this.getUserRole();
        const oldUserId = this.getUserId();

        // 调用父类方法更新基本信息
        super.updateUserInfo(userInfo);

        // 更新所有已初始化的子管理器的用户信息（直接更新，避免触发子管理器的事件）
        if (this.routeManager) {
            this.routeManager.userInfo = { ...this.routeManager.userInfo, ...this.userInfo };
            this.routeManager.clearCache();
            this.routeManager.loadPermissions();
        }
        if (this.componentManager) {
            this.componentManager.userInfo = { ...this.componentManager.userInfo, ...this.userInfo };
            this.componentManager.clearCache();
            this.componentManager.loadPermissions();
        }
        if (this.featureManager) {
            this.featureManager.userInfo = { ...this.featureManager.userInfo, ...this.userInfo };
            this.featureManager.clearCache();
            this.featureManager.loadPermissions();
        }
        if (this.conversationManager) {
            this.conversationManager.userInfo = { ...this.conversationManager.userInfo, ...this.userInfo };
            this.conversationManager.clearCache();
            this.conversationManager.loadPermissions();
        }
        // 区域权限管理器使用自己的updateUserInfo方法，不重新加载权限配置
        if (this.regionManager) {
            this.regionManager.updateUserInfo(this.userInfo);
        }

        const newRole = this.getUserRole();
        const newUserId = this.getUserId();

        // 只有主权限管理器触发权限变化事件
        if (oldRole !== newRole || oldUserId !== newUserId) {
            this.emitPermissionChange({
                type: 'userInfo',
                oldRole,
                newRole,
                oldUserId,
                newUserId,
                userInfo: this.userInfo,
                hasRoleChanged: oldRole !== newRole,
                hasUserChanged: oldUserId !== newUserId
            });
        }

        // 触发用户信息更新事件
        this.emitEvent('userInfoUpdated', this.userInfo);
    }



    /**
     * 清除所有权限缓存（重写基类方法，添加子管理器缓存清理）
     * @param {string} pattern - 可选的模式匹配，清除特定模式的缓存
     */
    clearCache(pattern = null) {
        // 调用父类方法清除自身缓存
        super.clearCache(pattern);

        // 清除所有子管理器的缓存
        if (this.routeManager) {
            this.routeManager.clearCache(pattern);
        }
        if (this.componentManager) {
            this.componentManager.clearCache(pattern);
        }
        if (this.featureManager) {
            this.featureManager.clearCache(pattern);
        }
        if (this.conversationManager) {
            this.conversationManager.clearCache(pattern);
        }
        if (this.regionManager) {
            this.regionManager.clearCache(pattern);
        }
    }

    /**
     * 配置所有管理器的缓存设置
     * @param {Object} config - 缓存配置
     */
    configureCaching(config) {
        // 配置主管理器缓存
        super.configureCaching(config);

        // 配置所有子管理器的缓存
        const managers = [
            this.routeManager,
            this.componentManager,
            this.featureManager,
            this.conversationManager,
            this.regionManager
        ];

        managers.forEach(manager => {
            if (manager && typeof manager.configureCaching === 'function') {
                manager.configureCaching(config);
            }
        });
    }

    /**
     * 获取所有管理器的缓存统计信息
     * @returns {Object} 缓存统计汇总
     */
    getAllCacheStats() {
        const stats = {
            main: this.getCacheStats(),
            route: this.routeManager ? this.routeManager.getCacheStats() : null,
            component: this.componentManager ? this.componentManager.getCacheStats() : null,
            feature: this.featureManager ? this.featureManager.getCacheStats() : null,
            conversation: this.conversationManager ? this.conversationManager.getCacheStats() : null,
            region: this.regionManager ? this.regionManager.getCacheStats() : null
        };

        // 计算总计
        let totalEntries = 0;
        let totalExpired = 0;
        let totalActive = 0;

        Object.values(stats).forEach(stat => {
            if (stat) {
                totalEntries += stat.totalEntries;
                totalExpired += stat.expiredEntries;
                totalActive += stat.activeEntries;
            }
        });

        stats.summary = {
            totalEntries,
            totalExpired,
            totalActive,
            cacheEnabled: this.cacheConfig.enableCache
        };

        return stats;
    }

    /**
     * 清理所有管理器的过期缓存
     */
    cleanAllExpiredCache() {
        // 清理主管理器过期缓存
        this.cleanExpiredCache();

        // 清理所有子管理器的过期缓存
        const managers = [
            this.routeManager,
            this.componentManager,
            this.featureManager,
            this.conversationManager,
            this.regionManager
        ];

        managers.forEach(manager => {
            if (manager && typeof manager.cleanExpiredCache === 'function') {
                manager.cleanExpiredCache();
            }
        });
    }

    /**
     * 获取当前用户信息
     * @returns {Object} 用户信息
     */
    getUserInfo() {
        return this.userInfo;
    }

    /**
     * 检查是否为管理员（重写基类方法，添加初始化检查和主任角色）
     * @returns {boolean} 是否为管理员
     */
    isAdmin() {
        if (!this.initialized) {
            return false;
        }
        const role = this.getUserRole();
        return role === USER_ROLE.ADMIN ||
               role === USER_ROLE.SUPER_ADMIN ||
               role === USER_ROLE.DIRECTOR;
    }

    /**
     * 检查是否为主任
     * @returns {boolean} 是否为主任
     */
    isDirector() {
        if (!this.initialized) {
            return false;
        }
        const role = this.getUserRole();
        return role === USER_ROLE.DIRECTOR;
    }

    /**
     * 检查是否为超级管理员（重写基类方法，添加初始化检查）
     * @returns {boolean} 是否为超级管理员
     */
    isSuperAdmin() {
        if (!this.initialized) {
            return false;
        }
        return super.isSuperAdmin();
    }

    /**
     * 检查是否已初始化
     * @returns {boolean} 是否已初始化
     */
    isInitialized() {
        return this.initialized;
    }

    /**
     * 检查区域功能是否启用
     * @param {string} functionName - 功能名称
     * @returns {boolean} 是否启用
     */
    isRegionFunctionEnabled(functionName) {
        if (!this.regionInitialized) {
            return false;
        }
        return this.regionManager.isFunctionEnabled(functionName);
    }

    /**
     * 获取所有启用的区域功能
     * @returns {Array<string>} 启用的功能列表
     */
    getEnabledRegionFunctions() {
        if (!this.regionInitialized) {
            return [];
        }
        return this.regionManager.getEnabledFunctions();
    }

    /**
     * 获取当前区域
     * @returns {string} 当前区域
     */
    getCurrentRegion() {
        if (!this.regionInitialized) {
            return '';
        }
        return this.regionManager.getCurrentRegion();
    }

    /**
     * 检查功能在当前区域是否可用
     * @param {string} functionName - 功能名称
     * @param {Object} options - 选项
     * @returns {boolean} 是否可用
     */
    isRegionFunctionAvailable(functionName, options = {}) {
        if (!this.regionInitialized) {
            return false;
        }
        return this.regionManager.isFunctionAvailable(functionName, options);
    }

    /**
     * 获取指定区域功能的所有映射权限
     * @param {string} regionFunction - 区域功能名称
     * @returns {Array<string>} 映射的权限列表
     */
    getRegionMappedPermissions(regionFunction) {
        if (!this.regionInitialized) {
            return [];
        }
        return this.regionManager.getMappedPermissions(regionFunction);
    }

    /**
     * 检查指定区域功能的所有映射权限
     * @param {string} regionFunction - 区域功能名称
     * @returns {Object} 映射权限的检查结果
     */
    checkAllRegionMappedPermissions(regionFunction) {
        if (!this.regionInitialized) {
            return {};
        }
        return this.regionManager.checkAllMappedPermissions(regionFunction);
    }

    /**
     * 获取所有启用的映射权限
     * @returns {Array<string>} 所有启用的映射权限列表
     */
    getAllEnabledMappedPermissions() {
        if (!this.regionInitialized) {
            return [];
        }
        return this.regionManager.getAllEnabledMappedPermissions();
    }

    /**
     * 获取权限映射关系摘要
     * @returns {Object} 权限映射摘要
     */
    getRegionPermissionMappingSummary() {
        if (!this.regionInitialized) {
            return {};
        }
        return this.regionManager.getPermissionMappingSummary();
    }

    /**
     * 设置用户在会话中的角色
     * @param {string} conversationId - 会话ID
     * @param {string} userId - 用户ID
     * @param {string} role - 角色 (owner|admin|member|muted)
     */
    setUserConversationRole(conversationId, userId, role) {
        if (!this.initialized || !this.conversationManager) {
            console.warn('ConversationPermissionManager not initialized');
            return;
        }
        this.conversationManager.setUserConversationRole(conversationId, userId, role);
    }

    /**
     * 获取用户在会话中的角色
     * @param {string} conversationId - 会话ID
     * @param {string} userId - 用户ID
     * @returns {string} 角色
     */
    getUserConversationRole(conversationId, userId) {
        if (!this.initialized || !this.conversationManager) {
            console.warn('ConversationPermissionManager not initialized');
            return 'member';
        }
        return this.conversationManager.getUserConversationRole(conversationId, userId);
    }

    /**
     * 批量设置会话成员角色
     * @param {string} conversationId - 会话ID
     * @param {Object} memberRoles - 成员角色映射 {userId: role}
     */
    setConversationMemberRoles(conversationId, memberRoles) {
        if (!this.initialized || !this.conversationManager) {
            console.warn('ConversationPermissionManager not initialized');
            return;
        }
        this.conversationManager.setConversationMemberRoles(conversationId, memberRoles);
    }

    /**
     * 检查是否为会话群主
     * @param {string} conversationId - 会话ID
     * @param {string} userId - 用户ID
     * @returns {boolean} 是否为群主
     */
    isConversationOwner(conversationId, userId) {
        if (!this.initialized || !this.conversationManager) {
            return false;
        }
        return this.conversationManager.isConversationOwner(conversationId, userId);
    }

    /**
     * 检查是否为会话管理员（包括群主）
     * @param {string} conversationId - 会话ID
     * @param {string} userId - 用户ID
     * @returns {boolean} 是否为管理员
     */
    isConversationAdmin(conversationId, userId) {
        if (!this.initialized || !this.conversationManager) {
            return false;
        }
        return this.conversationManager.isConversationAdmin(conversationId, userId);
    }

    /**
     * 获取用户在会话中的权限列表
     * @param {string} conversationId - 会话ID
     * @param {string} userId - 用户ID
     * @returns {Object} 权限列表
     */
    getUserConversationPermissions(conversationId, userId) {
        if (!this.initialized || !this.conversationManager) {
            return {};
        }
        return this.conversationManager.getUserConversationPermissions(conversationId, userId);
    }

    /**
     * 检查用户是否为会话创建者（参考 common_base.js）
     * @param {string} conversationId - 会话ID
     * @param {string} userId - 用户ID
     * @param {Object} conversationData - 会话数据（可选）
     * @returns {boolean} 是否为创建者
     */
    checkIsConversationCreator(conversationId, userId, conversationData = null) {
        if (!this.initialized || !this.conversationManager) {
            return false;
        }
        return this.conversationManager.checkIsCreator(conversationId, userId, conversationData);
    }

    /**
     * 检查用户是否为会话管理员（参考 common_base.js）
     * @param {string} conversationId - 会话ID
     * @param {string} userId - 用户ID
     * @param {Object} conversationData - 会话数据（可选）
     * @returns {boolean} 是否为管理员
     */
    checkIsConversationManager(conversationId, userId, conversationData = null) {
        if (!this.initialized || !this.conversationManager) {
            return false;
        }
        return this.conversationManager.checkIsManager(conversationId, userId, conversationData);
    }

    /**
     * 确保已初始化
     */
    ensureInitialized() {
        if (!this.initialized) {
            throw new Error('PermissionManager not initialized. Please call initialize() first.');
        }
    }

    /**
     * 退出登录时的权限清理（保留区域权限）
     * 只清理用户相关的权限管理器，保留区域权限不变
     */
    logoutCleanup() {
        console.log('执行退出登录权限清理，保留区域权限');

        // 停止缓存清理定时器
        this.stopCacheCleanup();

        // 销毁用户相关的权限管理器，保留区域权限管理器
        if (this.routeManager) {
            this.routeManager.destroy();
            this.routeManager = null;
        }
        if (this.componentManager) {
            this.componentManager.destroy();
            this.componentManager = null;
        }
        if (this.featureManager) {
            this.featureManager.destroy();
            this.featureManager = null;
        }
        if (this.conversationManager) {
            this.conversationManager.destroy();
            this.conversationManager = null;
        }

        // 注意：不销毁regionManager，保留区域权限
        // 只清理区域权限管理器的用户相关缓存
        if (this.regionManager) {
            this.regionManager.clearCache();
        }

        // 清理父类的用户相关数据，但不销毁整个管理器
        this.permissions.clear();
        this.cacheMetadata.clear();
        this.userInfo = null;
        this.initialized = false;

        // 注意：不重置regionInitialized，保持区域权限的初始化状态
        // 注意：不清除单例实例，保持权限管理器可用

        // 触发退出登录清理事件
        this.emitEvent('logoutCleanup');

        console.log('退出登录权限清理完成，区域权限已保留');
    }

    /**
     * 销毁权限管理器（重写基类方法，添加子管理器销毁逻辑）
     * 完全销毁所有权限管理器，包括区域权限
     */
    destroy() {
        // 停止缓存清理定时器
        this.stopCacheCleanup();

        // 销毁所有子管理器
        if (this.routeManager) {
            this.routeManager.destroy();
            this.routeManager = null;
        }
        if (this.componentManager) {
            this.componentManager.destroy();
            this.componentManager = null;
        }
        if (this.featureManager) {
            this.featureManager.destroy();
            this.featureManager = null;
        }
        if (this.conversationManager) {
            this.conversationManager.destroy();
            this.conversationManager = null;
        }
        if (this.regionManager) {
            this.regionManager.destroy();
            this.regionManager = null;
        }

        // 重置特有属性
        this.regionInitialized = false;

        // 清除单例实例
        PermissionManager.instance = null;

        // 调用父类销毁方法
        super.destroy();

        // 触发销毁事件
        this.emitEvent('destroyed');
    }

    /**
     * 启动缓存清理定时器
     */
    startCacheCleanup() {
        // 如果已经有定时器在运行，先停止
        this.stopCacheCleanup();

        // 启动新的定时器
        this.cacheCleanupInterval = setInterval(() => {
            try {
                this.cleanAllExpiredCache();
                console.log('PermissionManager: 定期清理过期缓存完成');
            } catch (error) {
                console.error('PermissionManager: 清理过期缓存时发生错误:', error);
            }
        }, this.cacheCleanupIntervalMs);

        console.log(`PermissionManager: 缓存清理定时器已启动，间隔 ${this.cacheCleanupIntervalMs / 1000} 秒`);
    }

    /**
     * 停止缓存清理定时器
     */
    stopCacheCleanup() {
        if (this.cacheCleanupInterval) {
            clearInterval(this.cacheCleanupInterval);
            this.cacheCleanupInterval = null;
            console.log('PermissionManager: 缓存清理定时器已停止');
        }
    }

    /**
     * 触发事件
     * @param {string} eventName - 事件名称
     * @param {any} data - 事件数据
     */
    emitEvent(eventName, data = null) {
        if (window.vm && window.vm.$emit) {
            window.vm.$emit(`permission:${eventName}`, data);
        }

        // 也可以使用自定义事件
        if (typeof window !== 'undefined' && window.dispatchEvent) {
            const event = new CustomEvent(`permission:${eventName}`, { detail: data });
            window.dispatchEvent(event);
        }
    }

    /**
     * 获取子管理器
     * @param {string} type - 管理器类型 (route|component|feature|region|conversation)
     * @returns {Object} 子管理器实例
     */
    getManager(type) {
        switch (type) {
        case 'route':
            if (!this.routeManager) {
                console.warn('RoutePermissionManager not initialized');
            }
            return this.routeManager;
        case 'component':
            if (!this.componentManager) {
                console.warn('ComponentPermissionManager not initialized');
            }
            return this.componentManager;
        case 'feature':
            if (!this.featureManager) {
                console.warn('FeaturePermissionManager not initialized');
            }
            return this.featureManager;
        case 'conversation':
            if (!this.conversationManager) {
                console.warn('ConversationPermissionManager not initialized');
            }
            return this.conversationManager;
        case 'region':
            if (!this.regionManager) {
                console.warn('RegionPermissionManager not initialized');
            }
            return this.regionManager;
        default:
            throw new Error(`Unknown manager type: ${type}`);
        }
    }


}

// 创建全局实例
const permissionManager = new PermissionManager();
window.permissionManager = permissionManager;
export default permissionManager;
export { PermissionManager };
