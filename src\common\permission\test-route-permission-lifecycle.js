/**
 * 路由权限生命周期测试
 * 验证退出登录后路由权限能否正确重新初始化
 */

import permissionManager from './index.js';
import { RoutePermissionManager } from './index.js';

/**
 * 测试路由权限的完整生命周期
 */
export async function testRoutePermissionLifecycle() {
    console.log('=== 开始测试路由权限生命周期 ===');

    try {
        // 1. 模拟应用启动时的路由权限管理器初始化
        console.log('1. 模拟应用启动时的路由权限管理器初始化...');
        const routeManager = RoutePermissionManager.getGlobalInstance();
        
        // 模拟路由配置
        const mockRouter = {
            options: {
                routes: [
                    {
                        path: '/admin',
                        meta: {
                            permissions: ['admin']
                        }
                    },
                    {
                        path: '/user',
                        meta: {
                            permissions: ['user']
                        }
                    }
                ]
            }
        };
        
        const whiteList = ['/login', '/register'];
        routeManager.setRouterConfig(mockRouter, whiteList);
        console.log('路由配置已设置:', routeManager.extractedRouteConfig);

        // 2. 模拟用户登录，初始化权限管理器
        console.log('2. 模拟用户登录，初始化权限管理器...');
        const mockUser = {
            uid: 'test_user_123',
            role: 2,
            username: 'test_user',
            nickname: '测试用户'
        };

        await permissionManager.initialize(mockUser);
        console.log('权限管理器初始化完成');

        // 3. 验证路由权限功能正常
        console.log('3. 验证路由权限功能...');
        const beforeLogout = {
            isInitialized: permissionManager.isInitialized(),
            routeManagerInitialized: permissionManager.routeManager?.isInitialized(),
            hasRouteConfig: !!permissionManager.routeManager?.extractedRouteConfig,
            routeConfigCount: Object.keys(permissionManager.routeManager?.extractedRouteConfig || {}).length,
            whiteListCount: permissionManager.routeManager?.whiteList?.length || 0
        };
        console.log('登录后状态:', beforeLogout);

        // 4. 执行退出登录清理
        console.log('4. 执行退出登录清理...');
        permissionManager.logoutCleanup();
        console.log('退出登录清理完成');

        // 5. 验证清理后的状态
        console.log('5. 验证清理后的状态...');
        const afterLogout = {
            isInitialized: permissionManager.isInitialized(),
            routeManagerExists: !!permissionManager.routeManager,
            routeManagerInitialized: permissionManager.routeManager?.isInitialized(),
            hasRouteConfig: !!permissionManager.routeManager?.extractedRouteConfig,
            routeConfigCount: Object.keys(permissionManager.routeManager?.extractedRouteConfig || {}).length,
            whiteListCount: permissionManager.routeManager?.whiteList?.length || 0,
            globalInstanceSame: permissionManager.routeManager === RoutePermissionManager.getGlobalInstance()
        };
        console.log('退出登录后状态:', afterLogout);

        // 6. 模拟重新登录
        console.log('6. 模拟重新登录...');
        const newMockUser = {
            uid: 'test_user_456',
            role: 1,
            username: 'new_test_user',
            nickname: '新测试用户'
        };

        await permissionManager.initialize(newMockUser);
        console.log('重新登录完成');

        // 7. 验证重新登录后的状态
        console.log('7. 验证重新登录后的状态...');
        const afterRelogin = {
            isInitialized: permissionManager.isInitialized(),
            routeManagerExists: !!permissionManager.routeManager,
            routeManagerInitialized: permissionManager.routeManager?.isInitialized(),
            hasRouteConfig: !!permissionManager.routeManager?.extractedRouteConfig,
            routeConfigCount: Object.keys(permissionManager.routeManager?.extractedRouteConfig || {}).length,
            whiteListCount: permissionManager.routeManager?.whiteList?.length || 0,
            globalInstanceSame: permissionManager.routeManager === RoutePermissionManager.getGlobalInstance(),
            userInfo: permissionManager.getUserInfo()
        };
        console.log('重新登录后状态:', afterRelogin);

        // 8. 验证结果
        console.log('8. 验证测试结果...');
        const validationResults = {
            routeConfigPreserved: afterLogout.hasRouteConfig && afterLogout.routeConfigCount === beforeLogout.routeConfigCount,
            whiteListPreserved: afterLogout.whiteListCount === beforeLogout.whiteListCount,
            globalInstancePreserved: afterLogout.globalInstanceSame,
            userDataCleared: !afterLogout.isInitialized && !afterLogout.routeManagerInitialized,
            reloginSuccessful: afterRelogin.isInitialized && afterRelogin.routeManagerInitialized,
            configStillAvailable: afterRelogin.hasRouteConfig && afterRelogin.routeConfigCount > 0,
            newUserDataSet: afterRelogin.userInfo?.uid === newMockUser.uid
        };

        console.log('验证结果:', validationResults);

        // 9. 测试结果
        const allTestsPassed = Object.values(validationResults).every(result => result === true);
        
        if (allTestsPassed) {
            console.log('✅ 所有测试通过！路由权限生命周期正常');
            console.log('✅ 路由配置在退出登录后得到保留');
            console.log('✅ 用户数据在退出登录后被正确清理');
            console.log('✅ 重新登录后路由权限功能正常');
        } else {
            console.log('❌ 测试失败！请检查实现');
            console.log('失败的验证项:', Object.entries(validationResults).filter(([key, value]) => !value));
        }

        return allTestsPassed;

    } catch (error) {
        console.error('❌ 测试过程中发生错误:', error);
        return false;
    }
}

/**
 * 在浏览器控制台中运行测试
 */
if (typeof window !== 'undefined') {
    window.testRoutePermissionLifecycle = testRoutePermissionLifecycle;
    console.log('路由权限生命周期测试已加载，可在控制台运行: window.testRoutePermissionLifecycle()');
}

export default testRoutePermissionLifecycle;
